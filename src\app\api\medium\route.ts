import { NextResponse } from 'next/server';
import Parser from 'rss-parser';

export async function GET() {
  const parser = new Parser({
    customFields: {
      item: ['content:encoded']
    }
  });
  const feed = await parser.parseURL('https://medium.com/feed/@dorddis');
  const posts = feed.items.slice(0, 5).map(item => {
    // Extract image from content:encoded or content
    let image = '';
    const content = item['content:encoded'] || item.content || '';
    const imgMatch = content.match(/<img[^>]+src="([^">]+)"/);
    if (imgMatch) image = imgMatch[1];
    // Extract subtitle: get the <h3> that comes after the first <h2>
    let subtitle = '';
    let subtitleMatch = null;
    // Find all <h3> tags
    const h3Matches = [...content.matchAll(/<h3[^>]*>(.*?)<\/h3>/g)];
    if (h3Matches.length > 0) {
      // If there is more than one <h3>, pick the first one that comes after the first <h2>
      const h2Match = content.match(/<h2[^>]*>(.*?)<\/h2>/);
      if (h2Match) {
        const h2Index = content.indexOf(h2Match[0]);
        // Find the first <h3> that comes after the <h2>
        for (const match of h3Matches) {
          const h3Index = content.indexOf(match[0]);
          if (h3Index > h2Index) {
            subtitleMatch = match;
            break;
          }
        }
      }
      // If not found, fallback to first <h3>
      if (!subtitleMatch) subtitleMatch = h3Matches[0];
    }
    if (subtitleMatch) {
      subtitle = subtitleMatch[1];
    } else {
      // fallback to first <p>
      const pMatch = content.match(/<p[^>]*>(.*?)<\/p>/);
      if (pMatch) subtitle = pMatch[1];
    }
    return {
      title: item.title,
      link: item.link,
      pubDate: item.pubDate,
      contentSnippet: item.contentSnippet,
      image,
      subtitle,
    };
  });
  return NextResponse.json(posts);
}