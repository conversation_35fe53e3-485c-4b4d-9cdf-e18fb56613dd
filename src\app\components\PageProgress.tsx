'use client';

import { motion, useScroll, useSpring } from 'framer-motion';

/**
 * Fixed vertical rail that fills as the user scrolls the entire document.
 * Import and drop <PageProgress /> once, directly inside <main>.
 */
export default function PageProgress() {
  /* useScroll without a target ⇒ whole page */
  const { scrollYProgress } = useScroll();

  /* spring for buttery-smooth fill */
  const fill = useSpring(scrollYProgress, {
    stiffness: 120,
    damping: 24,
    restDelta: 0.001,
  });

  return (
    <>
      {/* starting dot */}
      <div
        className="hidden md:flex fixed left-3 top-3 h-4 w-4 items-center
                   justify-center rounded-full border border-cyan-500 bg-white
                   shadow-lg z-30"
      >
        <div className="h-2 w-2 rounded-full border border-cyan-600 bg-emerald-500" />
      </div>

      {/* filling bar */}
      <motion.div
        style={{ scaleY: fill }}
        className="hidden md:block origin-top fixed left-3 top-8 w-1 h-full
                   rounded-full bg-gradient-to-b
                   from-blue-400 via-cyan-400 to-purple-500 z-20"
      />
    </>
  );
}
